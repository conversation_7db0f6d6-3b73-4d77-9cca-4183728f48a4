package xkv

import (
	"encoding/json"
	"log"
	"reflect"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/kv"
	"github.com/zeromicro/go-zero/core/stores/redis"

	"github.com/ProjectsTask/EasySwapBase/kit/convert"
)

const (
	// getAndDelScript 获取并删除key所关联的值lua脚本
	getAndDelScript = `local current = redis.call('GET', KEYS[1]);
if (current) then
    redis.call('DEL', KEYS[1]);
end
return current;`
)

// Store 键值存取器结构详情
type Store struct {
	kv.Store              // 适合简单的kv操作 比如 Get、Set、Del 等。
	Redis    *redis.Redis // 适合复杂的kv操作 比如 Lpush、Lpop、Hset、Sadd 等。
}

// NewStore 新建键值存取器
/*
  其实就是根据redes配置信息,链接redis。然后使用go-zero的kv.Store创建一个分布式键值存储器
  然后返回一个 Store 对象，包含连接信息
  --为什么不直接连接redis呢？
  因为go-zero的kv.Store支持多节点，可以实现分布式键值存储
  而直接连接redis只能连接一个节点，无法实现分布式键值存储


  这个项目其实属于单项目 多节点。EasySwapSync、EasySwapBase、EasySwapBackend
  虽然是独立项目，到那时操作相同的数据库以及缓存，所以可以考虑使用kv.Store，redis集群
  6381节点缓存的可能因为负载均衡，跑到了6371那，但是取是正常取到
*/
func NewStore(c kv.KvConf) *Store {
	// 证配置：确保有可用的缓存节点且权重合理
	if len(c) == 0 || cache.TotalWeights(c) <= 0 {
		log.Fatal("no cache nodes")
	}

	cn := redis.MustNewRedis(c[0].RedisConf) // 新建redis客户端 选择第一个节点
	// 返回一个 Store 对象，包含连接信息
	return &Store{
		Store: kv.NewStore(c), // 创建分布式键值存储器（支持多节点）
		Redis: cn,             // 第一个节点的客户端
	}
}

// GetInt 返回给定key所关联的int值
func (s *Store) GetInt(key string) (int, error) {
	value, err := s.Get(key)
	if err != nil {
		return 0, err
	}

	return convert.ToInt(value), nil
}

// SetInt 将int value关联到给定key，seconds为key的过期时间（秒）
func (s *Store) SetInt(key string, value int, seconds ...int) error {
	return s.SetString(key, convert.ToString(value), seconds...)
}

// GetInt64 返回给定key所关联的int64值
func (s *Store) GetInt64(key string) (int64, error) {
	value, err := s.Get(key)
	if err != nil {
		return 0, err
	}

	return convert.ToInt64(value), nil
}

// SetInt64 将int64 value关联到给定key，seconds为key的过期时间（秒）
func (s *Store) SetInt64(key string, value int64, seconds ...int) error {
	return s.SetString(key, convert.ToString(value), seconds...)
}

// GetBytes 返回给定key所关联的[]byte值
func (s *Store) GetBytes(key string) ([]byte, error) {
	value, err := s.Get(key)
	if err != nil {
		return nil, err
	}

	return []byte(value), nil
}

// GetDel 返回并删除给定key所关联的string值
func (s *Store) GetDel(key string) (string, error) {
	resp, err := s.Eval(getAndDelScript, key)
	if err != nil {
		return "", errors.Wrap(err, "eval script err")
	}

	return convert.ToString(resp), nil
}

// SetString 将string value关联到给定key，seconds为key的过期时间（秒）
func (s *Store) SetString(key, value string, seconds ...int) error {
	if len(seconds) != 0 {
		return errors.Wrapf(s.Setex(key, value, seconds[0]), "setex by seconds = %v err", seconds[0])
	}

	return errors.Wrap(s.Set(key, value), "set err")
}

// Read 将给定key所关联的值反序列化到obj对象
// 返回false时代表给定key不存在
func (s *Store) Read(key string, obj interface{}) (bool, error) {
	if !isValid(obj) {
		return false, errors.New("obj is invalid")
	}

	value, err := s.GetBytes(key)
	if err != nil {
		return false, errors.Wrap(err, "get bytes err")
	}
	if len(value) == 0 {
		return false, nil
	}

	err = json.Unmarshal(value, obj)
	if err != nil {
		return false, errors.Wrap(err, "json unmarshal value to obj err")
	}

	return true, nil
}

// Write 将对象obj序列化后关联到给定key，seconds为key的过期时间（秒）
func (s *Store) Write(key string, obj interface{}, seconds ...int) error {
	value, err := json.Marshal(obj)
	if err != nil {
		return errors.Wrap(err, "json marshal obj err")
	}

	return s.SetString(key, string(value), seconds...)
}

// GetFunc 给定key不存在时调用的数据获取函数
type GetFunc func() (interface{}, error)

// ReadOrGet 将给定key所关联的值反序列化到obj对象
// 若给定key不存在则调用数据获取函数，调用成功时赋值至obj对象
// 并将其序列化后关联到给定key，seconds为key的过期时间（秒）
func (s *Store) ReadOrGet(key string, obj interface{}, gf GetFunc, seconds ...int) error {
	isExist, err := s.Read(key, obj)
	if err != nil {
		return errors.Wrap(err, "read obj by err")
	}

	if !isExist {
		data, err := gf()
		if err != nil {
			return err
		}

		if !isValid(data) {
			return errors.New("get data is invalid")
		}

		ov, dv := reflect.ValueOf(obj).Elem(), reflect.ValueOf(data).Elem()
		if ov.Type() != dv.Type() {
			return errors.New("obj type and get data type are not equal")
		}
		ov.Set(dv)

		_ = s.Write(key, data, seconds...)
	}

	return nil
}

// isValid 判断对象是否合法
func isValid(obj interface{}) bool {
	if obj == nil {
		return false
	}

	if reflect.ValueOf(obj).Kind() != reflect.Ptr {
		return false
	}

	return true
}
