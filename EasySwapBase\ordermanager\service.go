// ===================== 订单管理相关 =====================
// 本文件包含订单管理器的主要结构体和核心方法。
// 专业注释：负责订单的入队、监听、过期处理、价格更新等。
// 通俗解释：这里是订单管理的“大管家”，负责订单的各种流转和处理。

package ordermanager

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/threading"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/ProjectsTask/EasySwapBase/logger/xzap"
	"github.com/ProjectsTask/EasySwapBase/stores/gdb/orderbookmodel/multi"
	"github.com/ProjectsTask/EasySwapBase/stores/xkv"
)

// ===================== 常量定义 =====================
// 专业注释：时间轮长度、队列前缀等常量
// 通俗解释：下面这些是用来配置订单管理用到的一些固定参数
const (
	WheelSize           = 3600 // 时间轮的长度，单位为秒，这里是1小时
	List                = 3
	CacheOrdersQueuePre = "cache:es:orders:%s" // 订单缓存队列的key前缀
)

// GenOrdersCacheKey 生成订单缓存队列的key
// 专业注释：根据链名生成redis队列key
// 通俗解释：拼接出存订单的redis队列名，比如 cache:es:orders:ethw
func GenOrdersCacheKey(chain string) string {
	return fmt.Sprintf(CacheOrdersQueuePre, chain)
}

// ===================== 结构体定义 =====================

// Order 订单结构体，链上订单的基本信息
// 专业注释：用来描述一个订单的基本内容，比如订单ID、藏品地址、链名等
// 通俗解释：一个订单的详细信息，后续处理都要用到
// （注意：本Order和multi.Order不是同一个结构体）
type Order struct {
	orderID        string
	CollectionAddr string
	ChainSuffix    string // 链名后缀，如ethw/bsc
	CycleCount     int64  // 订单生命周期
	WheelPosition  int64  // 在时间轮中的位置
	Next           *Order // 链表结构，指向下一个订单
}

// wheel 时间轮结构体
// 专业注释：每个wheel里是一个链表，存放订单
// 通俗解释：时间轮的每一格，里面可以有很多订单，按链表串起来
type wheel struct {
	NotifyActivities *Order // 链表头指针
}

// OrderManager 订单管理器
// 专业注释：负责订单的队列、过期、价格等管理
// 通俗解释：整个订单管理的“大管家”，里面有各种队列、数据库、锁等
type OrderManager struct {
	chain string // 当前链名

	TimeWheel          [WheelSize]wheel                // 时间轮，定时处理订单
	CurrentIndex       int64                           // 当前时间轮指针
	collectionOrders   map[string]*collectionTradeInfo // 每个藏品的订单信息
	collectionListedCh chan string                     // 新上架藏品的通知通道
	project            string                          // 项目名
	Xkv                *xkv.Store                      // redis存储 引入了xkv包 Store结构体，里面有kv.Store和redis.Redis，这里用队列操作 就用到了redis.Redis
	DB                 *gorm.DB                        // 数据库
	Ctx                context.Context
	Mux                *sync.RWMutex // 读写锁
}

// ===================== 构造函数 =====================
// New 创建订单管理器实例
// 专业注释：初始化OrderManager，把各种依赖都传进来
// 通俗解释：new一个订单管理器，后续所有订单相关操作都靠它
func New(ctx context.Context, db *gorm.DB, xkv *xkv.Store, chain string, project string) *OrderManager {
	return &OrderManager{
		chain:              chain,
		Xkv:                xkv,
		DB:                 db,
		Ctx:                ctx,
		Mux:                new(sync.RWMutex),
		collectionOrders:   make(map[string]*collectionTradeInfo),
		collectionListedCh: make(chan string, 1000),
		project:            project,
	}
}

// ===================== 启动与停止 =====================
// Start 启动订单管理器的各个后台任务
// 专业注释：开多个协程，分别处理新订单、订单过期、价格等
// 通俗解释：让订单管理器开始工作，自动处理各种订单相关的事情
func (om *OrderManager) Start() {
	threading.GoSafe(om.ListenNewListingLoop) // 处理新订单
	threading.GoSafe(om.orderExpiryProcess)   // 处理订单过期状态
	threading.GoSafe(om.floorPriceProcess)    // 处理floorprice更新
	threading.GoSafe(om.listCountProcess)     // 处理listCount更新
}

// Stop 停止订单管理器（目前未实现）
// 专业注释：预留接口，后续可加关闭逻辑
// 通俗解释：现在没写内容，后续可以加关闭和清理资源的代码
func (om *OrderManager) Stop() {
}

// ===================== 订单信息结构体 =====================
// ListingInfo 用于队列传递的订单关键信息
// 专业注释：只挑最重要的订单信息，方便序列化和存储
// 通俗解释：只保留最核心的订单信息，方便放进队列和redis
type ListingInfo struct {
	ExpireIn       int64           `json:"expire_in"`       // 过期时间
	OrderId        string          `json:"order_id"`        // 订单ID
	CollectionAddr string          `json:"collection_addr"` // 藏品合约地址
	TokenID        string          `json:"token_id"`        // TokenId
	Price          decimal.Decimal `json:"price"`           // 价格
	Maker          string          `json:"maker"`           // 卖家地址
}

// ===================== 新订单监听 =====================
// ListenNewListingLoop 循环监听redis队列，处理新上架订单
// 专业注释：不停地从redis队列里拿订单，判断是否过期，做后续处理
// 通俗解释：像工厂流水线一样，不断取出新订单，判断过期没，做相应处理
/*
ListenNewListingLoop  这个监听协程 不分是不是新订单，
只是从redis中取出一个订单，检查是否过期，过期地板价，
没过期也更新地板价, 并添加到订单过期检查队列
*/
func (om *OrderManager) ListenNewListingLoop() {
	key := GenOrdersCacheKey(om.chain)
	for {
		// 这里用到了redis.Redis的Lpop方法 ，但是省略了Redis,实际上可以是om.Xkv.Redis.Lpop(key)
		//  Lpop 移除并返回列表的第一个元素。
		result, err := om.Xkv.Lpop(key)
		if err != nil || result == "" {
			if err != nil && err != redis.Nil {
				xzap.WithContext(context.Background()).Warn("failed on get order from cache", zap.Error(err), zap.String("result", result))
			}
			time.Sleep(1 * time.Second)
			continue
		}

		// 打印日志
		xzap.WithContext(om.Ctx).Info("get listing from cache", zap.String("result", result))

		// 反序列化
		var listing ListingInfo
		if err := json.Unmarshal([]byte(result), &listing); err != nil { // 反序列化 将json字符串转换为ListingInfo结构体 赋值给listing
			xzap.WithContext(om.Ctx).Warn("failed on Unmarshal order info", zap.Error(err))
			continue
		}
		if listing.OrderId == "" { // 如果订单ID为空，则跳过
			xzap.WithContext(om.Ctx).Error("invalid null order id")
			continue
		}

		if listing.ExpireIn < time.Now().Unix() { // 订单已经过期
			xzap.WithContext(om.Ctx).Info("expired activity order", zap.String("order_id", listing.OrderId))

			// 更新订单状态 更新订单表中的order_status字段
			if err := om.updateOrdersStatus(listing.OrderId, multi.OrderStatusExpired); err != nil {
				xzap.WithContext(om.Ctx).Error("failed on update activity status", zap.String("order_id", listing.OrderId), zap.Error(err))
			}

			// 添加更新floorprice事件 更新地板价
			// 是为了解决恰好这个订单是这个NFT集合中最便宜的价格，这时候要更新地板价，地板价是影响这个集合的地板价
			if err := om.addUpdateFloorPriceEvent(&TradeEvent{
				EventType:      Expired,
				CollectionAddr: listing.CollectionAddr,
				TokenID:        listing.TokenID,
				OrderId:        listing.OrderId,
				From:           listing.Maker,
			}); err != nil {
				xzap.WithContext(om.Ctx).Error("failed on add update floor price event", zap.String("order_id", listing.OrderId), zap.Error(err))
			}
			continue
		} else { // 订单未过期
			if err := om.addUpdateFloorPriceEvent(&TradeEvent{ // 添加更新floorprice事件 更新地板价
				EventType:      Listing,
				CollectionAddr: listing.CollectionAddr,
				TokenID:        listing.TokenID,
				OrderId:        listing.OrderId,
				Price:          listing.Price,
				From:           listing.Maker,
			}); err != nil {
				xzap.WithContext(om.Ctx).Error("failed on push order to update price queue", zap.Error(err), zap.String("order_id", listing.OrderId),
					zap.String("order_id", listing.OrderId),
					zap.String("price", listing.Price.String()),
					zap.String("chain", om.chain))
			}

			// 添加到订单过期检查队列
			delaySeconds := listing.ExpireIn - time.Now().Unix()
			if err := om.addToOrderExpiryCheckQueue(delaySeconds, om.chain, listing.OrderId, listing.CollectionAddr); err != nil {
				xzap.WithContext(om.Ctx).Error("failed on push order to expired check queue", zap.Error(err), zap.String("order_id", listing.OrderId),
					zap.String("chain", om.chain))
			}
		}
	}
}

// ===================== 订单入队 =====================
// AddToOrderManagerQueue 将订单添加到订单管理队列中。
// 专业注释：
// 1. 检查订单的 TokenId 是否为空，若为空则返回错误。
// 2. 将订单关键信息序列化为 JSON 格式。
// 3. 将序列化后的订单信息推入队列（使用 Xkv 的 Lpush 方法）。
// 4. 若过程中有任何错误，均返回带有详细信息的错误。
//
// 通俗解释：
// 1. 先检查订单有没有 TokenId，没有就报错，因为每个订单都必须有 TokenId。
// 2. 把订单的主要信息（比如过期时间、订单ID、藏品地址、TokenId、价格、卖家地址）打包成 JSON 字符串，方便存储。
// 3. 把这个字符串放到一个队列里，后续可以依次处理这些订单。
// 4. 如果中间有任何一步出错了，就把错误信息返回，方便排查问题。
func (om *OrderManager) AddToOrderManagerQueue(order *multi.Order) error {
	// 1. 检查订单的 TokenId 是否为空
	if order.TokenId == "" {
		return errors.New("order manger need token id") // TokenId 不能为空，否则报错
	}

	// 2. 将订单信息序列化为 JSON 格式
	rawInfo, err := json.Marshal(ListingInfo{ // 将订单信息序列化
		ExpireIn:       order.ExpireTime,
		OrderId:        order.OrderID,
		CollectionAddr: order.CollectionAddress,
		TokenID:        order.TokenId,
		Price:          order.Price,
		Maker:          order.Maker,
	})
	if err != nil {
		return errors.Wrap(err, "failed on marshal listing info") // 序列化失败，返回错误
	}

	// 3. 将序列化后的订单信息推入队列 这里用到了redis.Redis的Lpush方法
	// Lpush 将一个或多个值插入到列表头部。
	// Rpush 将一个或多个值插入到列表尾部。
	if _, err := om.Xkv.Lpush(GenOrdersCacheKey(om.chain), string(rawInfo)); err != nil {
		return errors.Wrap(err, "failed on add to queue") // 推入队列失败，返回错误
	}

	// 4. 正常返回 nil，表示添加成功
	return nil
}
