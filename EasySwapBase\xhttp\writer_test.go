package xhttp

import (
	"testing"
)

func TestLogDetails(t *testing.T) {
	t.Log("200 - [::1]:63020\n=\u003e POST /v2/notify/sms HTTP/1.1\n{\n  \"phone\": \"18000000000\",\n  \"template_id\": 0\n}\n\u003c= {\"trace_id\":\"f5d993c16fa813d2\",\"code\":200,\"msg\":\"OK\",\"data\":{\"phone\":\"18000000000\"}}")
	t.Log("recoverhandler.go:16 (/v2/admin/member?id=1 - [::1]:60773) panic test\ngoroutine 161 [running]:\nruntime/debug.Stack(0xc000b41800, 0x1ad0b00, 0x1e2eb60)\n\t/usr/local/go/src/runtime/debug/stack.go:24 +0x9d\ngithub.com/zeromicro/go-zero/rest/handler.RecoverHandler.func1.1(0xc00063a500, 0x1e61080, 0xc00068f380)\n\t/Users/<USER>/go/pkg/mod/github.com/zeromicro/go-zero@v1.1.8/rest/handler/recoverhandler.go:16 +0x5a\npanic(0x1ad0b00, 0x1e2eb60)\n\t/usr/local/go/src/runtime/panic.go:969 +0x166\ngitlab.com/ponyW/gatemicro/gateway/api/v2/internal/logic/user.(*GetMemberByAdminLogic).GetMemberByAdmin(0xc000b41a10, 0x1, 0xc000b70090, 0x0, 0x0)\n\t/Users/<USER>/go/gitlab.com/ponyW/gatemicro/gateway/api/v2/internal/logic/user/getmemberbyadminlogic.go:33 +0x39\ngitlab.com/ponyW/gatemicro/gateway/api/v2/internal/handler/user.GetMemberByAdminHandler.func1(0x1e5e8c0, 0xc0004a4570, 0xc000b2c000)\n\t/Users/<USER>/go/gitlab.com/ponyW/gatemicro/gateway/api/v2/internal/handler/user/getmemberbyadminhandler.go:29 +0x161\ngitlab.com/ponyW/gatemicro/gateway/api/v2/internal/middleware.(*PrivilegeMiddleware).Handle.func1(0x1e5e8c0, 0xc0004a4570, 0xc000b2c000)\n\t/Users/<USER>/go/gitlab.com/ponyW/gatemicro/gateway/api/v2/internal/middleware/privilegemiddleware.go:61 +0x2de\ngitlab.com/ponyW/gatemicro/gateway/api/v2/internal/middleware.(*JWTMiddleware).Handle.func1(0x1e5e8c0, 0xc0004a4570, 0xc00063a500)\n\t/Users/<USER>/go/gitlab.com/ponyW/gatemicro/gateway/api/v2/internal/middleware/jwtmiddleware.go:52 +0x343\ngitlab.com/ponyW/gatemicro/gateway/api/v2/internal/middleware.(*RLogMiddleware).Handle.func1(0x1e61080, 0xc00068f380, 0xc00063a500)\n\t/Users/<USER>/go/gitlab.com/ponyW/gatemicro/gateway/api/v2/internal/middleware/rlogmiddleware.go:32 +0x2b3\nnet/http.HandlerFunc.ServeHTTP(0xc0004a50f0, 0x1e61080, 0xc00068f380, 0xc00063a500)\n\t/usr/local/go/src/net/http/server.go:2041 +0x44\ngitlab.com/ponyW/gatemicro/gateway/api/v2/internal/middleware.(*CorsMiddleware).Handle.func1(0x1e61080, 0xc00068f380, 0xc00063a500)\n\t/Users/<USER>/go/gitlab.com/ponyW/gatemicro/gateway/api/v2/internal/middleware/corsmiddleware.go:27 +0x89\nnet/http.HandlerFunc.ServeHTTP(0xc00021ce40, 0x1e61080, 0xc00068f380, 0xc00063a500)\n\t/usr/local/go/src/net/http/server.go:2041 +0x44\ngithub.com/zeromicro/go-zero/rest/handler.GunzipHandler.func1(0x1e61080, 0xc00068f380, 0xc00063a500)\n\t/Users/<USER>/go/pkg/mod/github.com/zeromicro/go-zero@v1.1.8/rest/handler/gunziphandler.go:26 +0xc1\nnet/http.HandlerFunc.ServeHTTP(0xc00050db40, 0x1e61080, 0xc00068f380, 0xc00063a500)\n\t/usr/local/go/src/net/http/server.go:2041 +0x44\ngithub.com/zeromicro/go-zero/rest/handler.MaxBytesHandler.func2.1(0x1e61080, 0xc00068f380, 0xc00063a500)\n\t/Users/<USER>/go/pkg/mod/github.com/zeromicro/go-zero@v1.1.8/rest/handler/maxbyteshandler.go:24 +0x148\nnet/http.HandlerFunc.ServeHTTP(0xc00050db60, 0x1e61080, 0xc00068f380, 0xc00063a500)\n\t/usr/local/go/src/net/http/server.go:2041 +0x44\ngithub.com/zeromicro/go-zero/rest/handler.MetricHandler.func1.1(0x1e61080, 0xc00068f380, 0xc00063a500)\n\t/Users/<USER>/go/pkg/mod/github.com/zeromicro/go-zero@v1.1.8/rest/handler/metrichandler.go:21 +0xc9\nnet/http.HandlerFunc.ServeHTTP(0xc00050db80, 0x1e61080, 0xc00068f380, 0xc00063a500)\n\t/usr/local/go/src/net/http/server.go:2041 +0x44\ngithub.com/zeromicro/go-zero/rest/handler.RecoverHandler.func1(0x1e61080, 0xc00068f380, 0xc00063a500)\n\t/Users/<USER>/go/pkg/mod/github.com/zeromicro/go-zero@v1.1.8/rest/handler/recoverhandler.go:21 +0x83\nnet/http.HandlerFunc.ServeHTTP(0xc00050dba0, 0x1e61080, 0xc00068f380, 0xc00063a500)\n\t/usr/local/go/src/net/http/server.go:2041 +0x44\nnet/http.(*timeoutHandler).ServeHTTP.func1(0xc00068f3e0, 0xc000331500, 0xc00068f380, 0xc00063a500, 0xc0002e0f00)\n\t/usr/local/go/src/net/http/server.go:3267 +0x7f\ncreated by net/http.(*timeoutHandler).ServeHTTP\n\t/usr/local/go/src/net/http/server.go")
	t.Log("interceptor.go:150 panic test goroutine 125 [running]:\nruntime/debug.Stack(0x1ad3de0, 0xc00055e3f0, 0x2)\n\t/usr/local/go/src/runtime/debug/stack.go:24 +0x9d\ngitlab.com/ponyW/gatemicro/pkg/errcode.toPanicError(0x1d082e0, 0xc000616ac0, 0x19e0ee0, 0x1cd94f0, 0x2, 0x27b81c0)\n\t/Users/<USER>/go/gitlab.com/ponyW/gatemicro/pkg/errcode/interceptor.go:150 +0x5f\ngitlab.com/ponyW/gatemicro/pkg/errcode.CrashInterceptor.func1(0x19e0ee0, 0x1cd94f0)\n\t/Users/<USER>/go/gitlab.com/ponyW/gatemicro/pkg/errcode/interceptor.go:126 +0x50\ngitlab.com/ponyW/gatemicro/pkg/errcode.handleCrash(0xc0001bbe58)\n\t/Users/<USER>/go/gitlab.com/ponyW/gatemicro/pkg/errcode/interceptor.go:144 +0x57\npanic(0x19e0ee0, 0x1cd94f0)\n\t/usr/local/go/src/runtime/panic.go:969 +0x166\ngitlab.com/ponyW/gatemicro/service/rpc/launchpad/internal/logic.(*GetMemberLogic).GetMember(0xc0001bbbe0, 0xc000424300, 0x2424c01, 0xc000425260, 0xc0001bbc00)\n\t/Users/<USER>/go/gitlab.com/ponyW/gatemicro/service/rpc/launchpad/internal/logic/getmemberlogic.go:34 +0x39\ngitlab.com/ponyW/gatemicro/service/rpc/launchpad/internal/server.(*LaunchpadServer).GetMember(0xc000422258, 0x1d083a0, 0xc000425260, 0xc000424300, 0xc000422258, 0x1d083a0, 0xc000425260)\n\t/Users/<USER>/go/gitlab.com/ponyW/gatemicro/service/rpc/launchpad/internal/server/launchpadserver.go:81 +0xb9\ngitlab.com/ponyW/gatemicro/service/rpc/launchpad/launchpad._Launchpad_GetMember_Handler.func1(0x1d083a0, 0xc000425260, 0x1aa5f80, 0xc000424300, 0x11266df, 0x0, 0xc00047d530, 0x100e4a6)\n\t/Users/<USER>/go/gitlab.com/ponyW/gatemicro/service/rpc/launchpad/launchpad/launchpadservice.pb.go:6252 +0x86\ngitlab.com/ponyW/gatemicro/pkg/jwt.TokenInterceptor(0x1d082e0, 0xc000616ac0, 0x1aa5f80, 0xc000424300, 0xc0004ab0a0, 0xc0004ab0c0, 0xc0004ab0c0, 0x9, 0x9, 0xc00056e800)\n\t/Users/<USER>/go/gitlab.com/ponyW/gatemicro/pkg/jwt/interceptor.go:85 +0x6a\ngoogle.golang.org/grpc.getChainUnaryHandler.func1(0x1d082e0, 0xc000616ac0, 0x1aa5f80, 0xc000424300, 0x1, 0xc000616b40, 0xc00047d580, 0x15ba9c8)\n\t/Users/<USER>/go/pkg/mod/google.golang.org/grpc@v1.38.0/server.go:1127 +0xe7\ngitlab.com/ponyW/gatemicro/pkg/errcode.ErrInterceptor(0x1d082e0, 0xc000616ac0, 0x1aa5f80, 0xc000424300, 0xc0004ab0a0, 0xc000616b40, 0xc000616b40, 0x1014f9a, 0x1b8e3a1, 0x38)\n\t/Users/<USER>/go/gitlab.com/ponyW/gatemicro/pkg/errcode/interceptor.go:50 +0x55\ngoogle.golang.org/grpc.getChainUnaryHandler.func1(0x1d082e0, 0xc000616ac0, 0x1aa5f80, 0xc000424300, 0xc00047d758, 0xc00047d668, 0x100ed18, 0x40)\n\t/Users/<USER>/go/pkg/mod/google.golang.org/grpc@v1.38.0/server.go:1127 +0xe7\ngitlab.com/ponyW/gatemicro/pkg/errcode.CrashInterceptor(0x1d082e0, 0xc000616ac0, 0x1aa5f80, 0xc000424300, 0xc0004ab0a0, 0xc000616b00, 0x0, 0x0, 0x0, 0x0)\n\t/Users/<USER>/go/gitlab.com/ponyW/gatemicro/pkg/errcode/interceptor.go:129 +0xc9\ngoogle.golang.org/grpc.getChainUnaryHandler.func1(0x1d082e0, 0xc000616ac0, 0x1aa5f80, 0xc000424300, 0xc000205440, 0x1bceb60, 0x100000000000000, 0xc0007822b0)\n\t/Users/<USER>/go/pkg/mod/google.golang.org/grpc@v1.38.0/server.go:1127 +0xe7\ngithub.com/zeromicro/go-zero/zrpc/internal/serverinterceptors.UnaryTimeoutInterceptor.func1.1(0xc0000471a0, 0xc000782460, 0xc000616a80, 0x1d082e0, 0xc000616ac0, 0x1aa5f80, 0xc000424300, 0xc000658590, 0xc0006585a0, 0xc000652240)\n\t/Users/<USER>/go/pkg/mod/github.com/zeromicro/go-zero@v1.1.8/zrpc/internal/serverinterceptors/timeoutinterceptor.go:33 +0xc2\ncreated by github.com/zeromicro/go-zero/zrpc/internal/serverinterceptors.UnaryTimeoutInterceptor.func1\n\t/Users/<USER>/go/pkg/mod/github.com/zeromicro/go-zero@v1.1.8/zrpc/internal/serverinterceptors/timeoutinterceptor.go:24 +0x1d7\n")
	t.Log("interceptor.go:149 panic test [running]:\nruntime.gopanic\n\truntime/panic.go:969\ngitlab.com/ponyW/gatemicro/service/rpc/launchpad/internal/logic.(*GetMemberLogic).GetMember\n\tgitlab.com/ponyW/gatemicro/service/rpc/launchpad/internal/logic/getmemberlogic.go:34\ngitlab.com/ponyW/gatemicro/service/rpc/launchpad/internal/server.(*LaunchpadServer).GetMember\n\tgitlab.com/ponyW/gatemicro/service/rpc/launchpad/internal/server/launchpadserver.go:81\ngitlab.com/ponyW/gatemicro/service/rpc/launchpad/launchpad._Launchpad_GetMember_Handler.func1\n\tgitlab.com/ponyW/gatemicro/service/rpc/launchpad/launchpad/launchpadservice.pb.go:6252\ngitlab.com/ponyW/gatemicro/pkg/jwt.TokenInterceptor\n\tgitlab.com/ponyW/gatemicro/pkg/jwt/interceptor.go:85\ngitlab.com/ponyW/gatemicro/pkg/errcode.ErrInterceptor\n\tgitlab.com/ponyW/gatemicro/pkg/errcode/interceptor.go:52\ngitlab.com/ponyW/gatemicro/pkg/errcode.CrashInterceptor\n\tgitlab.com/ponyW/gatemicro/pkg/errcode/interceptor.go:133")
}
